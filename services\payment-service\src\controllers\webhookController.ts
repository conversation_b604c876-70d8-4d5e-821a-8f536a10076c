import { Request, Response } from 'express';
import { Payment } from '../models/Payment';
import { PaymentStatus } from '../../shared/types';
import { xenditService } from '../services/xenditService';
import { subscriptionService } from '../services/subscriptionService';
import { logger } from '../utils/logger';

export const handleXenditWebhook = async (req: Request, res: Response) => {
  try {
    const signature = req.headers['x-callback-token'] as string;
    const rawBody = JSON.stringify(req.body);

    // Verify webhook signature
    if (!xenditService.verifyWebhookSignature(rawBody, signature)) {
      logger.warn('Invalid webhook signature');
      return res.status(400).json({
        success: false,
        message: 'Invalid signature'
      });
    }

    const webhookData = xenditService.processWebhook(req.body);
    logger.info(`Webhook received: ${webhookData.event} for ${webhookData.externalId}`);

    // Find payment by external ID
    const payment = await Payment.findOne({ externalId: webhookData.externalId });
    if (!payment) {
      logger.warn(`Payment not found for external ID: ${webhookData.externalId}`);
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Update payment based on webhook event
    switch (webhookData.status) {
      case 'PAID':
        if (payment.status !== PaymentStatus.COMPLETED) {
          payment.status = PaymentStatus.COMPLETED;
          payment.transactionId = webhookData.invoiceId;
          payment.paidAt = webhookData.paidAt || new Date();
          payment.webhookData = req.body;
          await payment.save();

          // Create subscription
          try {
            await subscriptionService.createSubscription(
              payment.studentId,
              payment.courseId,
              payment.id,
              90, // Default 90 days - should come from course data
              payment.amount,
              payment.currency
            );
            
            logger.info(`Subscription created for payment: ${payment.id}`);
          } catch (error) {
            logger.error('Error creating subscription from webhook:', error);
          }
        }
        break;

      case 'EXPIRED':
        payment.status = PaymentStatus.FAILED;
        payment.failureReason = 'Payment expired';
        payment.webhookData = req.body;
        await payment.save();
        break;

      case 'FAILED':
        payment.status = PaymentStatus.FAILED;
        payment.failureReason = 'Payment failed';
        payment.webhookData = req.body;
        await payment.save();
        break;

      default:
        logger.info(`Unhandled webhook status: ${webhookData.status}`);
    }

    res.json({
      success: true,
      message: 'Webhook processed successfully'
    });
  } catch (error) {
    logger.error('Error processing webhook:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};