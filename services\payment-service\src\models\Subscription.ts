import mongoose, { Document, Schema } from 'mongoose';
import { Subscription as ISubscription, SubscriptionStatus } from '../../shared/types';

export interface SubscriptionDocument extends ISubscription, Document {}

const subscriptionSchema = new Schema<SubscriptionDocument>({
  studentId: { type: String, required: true, index: true },
  courseId: { type: String, required: true, index: true },
  status: { 
    type: String, 
    enum: Object.values(SubscriptionStatus),
    default: SubscriptionStatus.ACTIVE 
  },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  failureCount: { type: Number, default: 0, min: 0 },
  maxFailures: { type: Number, default: 3 },
  paymentId: { type: String, required: true },
  amount: { type: Number, required: true, min: 0 },
  currency: { type: String, default: 'IDR' },
  isEligibleForCoaching: { type: Boolean, default: false },
  terminatedAt: { type: Date },
  terminationReason: { type: String },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Compound index for student and course
subscriptionSchema.index({ studentId: 1, courseId: 1 });
subscriptionSchema.index({ status: 1 });
subscriptionSchema.index({ endDate: 1 });

// Update updatedAt field
subscriptionSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

export const Subscription = mongoose.model<SubscriptionDocument>('Subscription', subscriptionSchema);