import express from 'express';
import { auth, authorize } from '../middleware/auth';
import { UserRole } from '../../shared/types';
import { PlatformAnalytics, CourseAnalytics, UserAnalytics } from '../models/Analytics';
import { logger } from '../utils/logger';

const router = express.Router();

interface AuthRequest extends express.Request {
  user?: any;
}

// Generate platform report (Admin only)
router.get('/platform', auth, authorize(UserRole.ADMIN), async (req: AuthRequest, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const filter: any = {};
    if (startDate && endDate) {
      filter.date = {
        $gte: new Date(startDate as string),
        $lte: new Date(endDate as string)
      };
    }
    
    const analytics = await PlatformAnalytics.find(filter).sort({ date: -1 });
    
    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    logger.error('Error generating platform report:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Generate course performance report
router.get('/courses', auth, authorize(UserRole.TUTOR, UserRole.ADMIN), async (req: AuthRequest, res) => {
  try {
    const { tutorId } = req.query;
    
    const filter: any = {};
    if (req.user.role === UserRole.TUTOR) {
      filter.tutorId = req.user.id;
    } else if (tutorId) {
      filter.tutorId = tutorId;
    }
    
    const courses = await CourseAnalytics.find(filter).sort({ totalEnrollments: -1 });
    
    res.json({
      success: true,
      data: courses
    });
  } catch (error) {
    logger.error('Error generating course report:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Generate user activity report (Admin only)
router.get('/users', auth, authorize(UserRole.ADMIN), async (req: AuthRequest, res) => {
  try {
    const { role } = req.query;
    
    const filter: any = {};
    if (role) {
      filter.role = role;
    }
    
    const users = await UserAnalytics.find(filter).sort({ lastActivity: -1 });
    
    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    logger.error('Error generating user report:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

export default router;