import mongoose, { Document, Schema } from 'mongoose';
import { Payment as IPayment, PaymentStatus } from '../../shared/types';

export interface PaymentDocument extends IPayment, Document {}

const paymentSchema = new Schema<PaymentDocument>({
  studentId: { type: String, required: true, index: true },
  courseId: { type: String, required: true, index: true },
  amount: { type: Number, required: true, min: 0 },
  currency: { type: String, default: 'IDR' },
  status: { 
    type: String, 
    enum: Object.values(PaymentStatus),
    default: PaymentStatus.PENDING 
  },
  paymentMethod: { type: String, required: true },
  transactionId: { type: String }, // Xendit transaction ID
  invoiceId: { type: String }, // Xendit invoice ID
  externalId: { type: String, required: true, unique: true }, // Our unique reference
  paymentUrl: { type: String }, // Xendit payment URL
  expiryDate: { type: Date },
  paidAt: { type: Date },
  failureReason: { type: String },
  webhookData: { type: Schema.Types.Mixed }, // Store webhook payload
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes for better performance
paymentSchema.index({ externalId: 1 }, { unique: true });
paymentSchema.index({ invoiceId: 1 });
paymentSchema.index({ transactionId: 1 });
paymentSchema.index({ status: 1 });

// Update updatedAt field
paymentSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

export const Payment = mongoose.model<PaymentDocument>('Payment', paymentSchema);