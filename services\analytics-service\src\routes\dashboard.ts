import express from 'express';
import { auth, authorize } from '../middleware/auth';
import { UserRole } from '../../shared/types';
import { dataCollectionService } from '../services/dataCollectionService';
import { logger } from '../utils/logger';

const router = express.Router();

interface AuthRequest extends express.Request {
  user?: any;
}

// Admin dashboard
router.get('/admin', auth, authorize(UserRole.ADMIN), async (req: AuthRequest, res) => {
  try {
    const dashboard = await dataCollectionService.getPlatformDashboard();
    
    res.json({
      success: true,
      data: dashboard
    });
  } catch (error) {
    logger.error('Error getting admin dashboard:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Tutor dashboard
router.get('/tutor', auth, authorize(UserRole.TUTOR), async (req: AuthRequest, res) => {
  try {
    const tutorId = req.user.id;
    const dashboard = await dataCollectionService.getTutorDashboard(tutorId);
    
    res.json({
      success: true,
      data: dashboard
    });
  } catch (error) {
    logger.error('Error getting tutor dashboard:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

export default router;