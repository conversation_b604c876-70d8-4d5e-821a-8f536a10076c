import express from 'express';
import { body } from 'express-validator';
import { validate } from '../middleware/validation';
import { auth, authorize } from '../middleware/auth';
import { UserRole } from '../../shared/types';
import {
  createAssignment,
  getAssignmentsByLesson,
  getAssignmentById,
  submitAssignment,
  getAssignmentSubmissions,
  updateAssignment,
  deleteAssignment
} from '../controllers/assignmentController';

const router = express.Router();

// Get assignments by lesson
router.get('/lesson/:lessonId', getAssignmentsByLesson);

// Get assignment by ID
router.get('/:id', getAssignmentById);

// Create assignment (tutor/admin only)
router.post('/', [
  auth,
  authorize(UserRole.TUTOR, UserRole.ADMIN),
  body('lessonId').notEmpty().withMessage('Lesson ID is required'),
  body('title').notEmpty().withMessage('Assignment title is required'),
  body('questions').isArray({ min: 1 }).withMessage('At least one question is required'),
  body('triggerTime').isInt({ min: 0 }).withMessage('Trigger time must be a positive integer'),
  body('timeLimit').isInt({ min: 1 }).withMessage('Time limit must be a positive integer'),
  validate
], createAssignment);

// Submit assignment (student only)
router.post('/:id/submit', [
  auth,
  authorize(UserRole.STUDENT),
  body('answers').isArray().withMessage('Answers must be an array'),
  body('subscriptionId').notEmpty().withMessage('Subscription ID is required'),
  validate
], submitAssignment);

// Get assignment submissions (student only)
router.get('/:id/submissions', auth, authorize(UserRole.STUDENT), getAssignmentSubmissions);

// Update assignment (tutor/admin only)
router.put('/:id', auth, authorize(UserRole.TUTOR, UserRole.ADMIN), updateAssignment);

// Delete assignment (tutor/admin only)
router.delete('/:id', auth, authorize(UserRole.TUTOR, UserRole.ADMIN), deleteAssignment);

export default router;