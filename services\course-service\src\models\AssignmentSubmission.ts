import mongoose, { Document, Schema } from 'mongoose';
import { AssignmentSubmission as IAssignmentSubmission, Answer } from '../../shared/types';

export interface AssignmentSubmissionDocument extends IAssignmentSubmission, Document {}

const answerSchema = new Schema<Answer>({
  questionId: { type: String, required: true },
  answer: { type: String, required: true },
  isCorrect: { type: Boolean, required: true },
  points: { type: Number, required: true, min: 0 }
});

const assignmentSubmissionSchema = new Schema<AssignmentSubmissionDocument>({
  studentId: { type: String, required: true, index: true },
  assignmentId: { type: String, required: true, index: true },
  subscriptionId: { type: String, required: true },
  answers: [answerSchema],
  score: { type: Number, required: true, min: 0, max: 100 },
  isPassed: { type: Boolean, required: true },
  attemptNumber: { type: Number, required: true, min: 1 },
  timeSpent: { type: Number }, // in seconds
  submittedAt: { type: Date, default: Date.now },
  gradedAt: { type: Date, default: Date.now },
  feedback: { type: String }
});

// Compound indexes
assignmentSubmissionSchema.index({ studentId: 1, assignmentId: 1, attemptNumber: 1 }, { unique: true });
assignmentSubmissionSchema.index({ subscriptionId: 1 });
assignmentSubmissionSchema.index({ submittedAt: -1 });

export const AssignmentSubmission = mongoose.model<AssignmentSubmissionDocument>('AssignmentSubmission', assignmentSubmissionSchema);