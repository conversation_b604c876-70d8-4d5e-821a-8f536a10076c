import express from 'express';
import { auth, authorize } from '../middleware/auth';
import { UserRole } from '../../shared/types';
import { progressService } from '../services/progressService';
import { logger } from '../utils/logger';

const router = express.Router();

interface AuthRequest extends express.Request {
  user?: any;
}

// Get student progress for a course
router.get('/course/:courseId', auth, authorize(UserRole.STUDENT), async (req: AuthRequest, res) => {
  try {
    const { courseId } = req.params;
    const studentId = req.user.id;

    const progressDetails = await progressService.getProgressDetails(studentId, courseId);

    res.json({
      success: true,
      data: progressDetails
    });
  } catch (error) {
    logger.error('Error getting progress:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get pacing status for a course
router.get('/course/:courseId/pacing', auth, authorize(UserRole.STUDENT), async (req: AuthRequest, res) => {
  try {
    const { courseId } = req.params;
    const { subscriptionStartDate, courseDuration } = req.query;
    const studentId = req.user.id;

    if (!subscriptionStartDate || !courseDuration) {
      return res.status(400).json({
        success: false,
        message: 'Subscription start date and course duration are required'
      });
    }

    const pacing = await progressService.calculatePacing(
      studentId,
      courseId,
      new Date(subscriptionStartDate as string),
      parseInt(courseDuration as string)
    );

    res.json({
      success: true,
      data: pacing
    });
  } catch (error) {
    logger.error('Error getting pacing status:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Complete a lesson
router.post('/lesson/:lessonId/complete', auth, authorize(UserRole.STUDENT), async (req: AuthRequest, res) => {
  try {
    const { lessonId } = req.params;
    const { courseId } = req.body;
    const studentId = req.user.id;

    await progressService.completeLesson(studentId, courseId, lessonId);

    res.json({
      success: true,
      message: 'Lesson completed successfully'
    });
  } catch (error) {
    logger.error('Error completing lesson:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

export default router;