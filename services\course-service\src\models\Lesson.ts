import mongoose, { Document, Schema } from 'mongoose';
import { Lesson as ILesson, Material, MaterialType } from '../../shared/types';

export interface LessonDocument extends ILesson, Document {}

const materialSchema = new Schema<Material>({
  type: { 
    type: String, 
    enum: Object.values(MaterialType),
    required: true 
  },
  title: { type: String, required: true },
  url: { type: String, required: true },
  order: { type: Number, required: true }
});

const lessonSchema = new Schema<LessonDocument>({
  moduleId: { type: String, required: true, index: true },
  title: { type: String, required: true, trim: true },
  description: { type: String, required: true },
  videoUrl: { type: String }, // Bunny.net video URL
  videoId: { type: String }, // Bunny.net video ID
  duration: { type: Number, default: 0 }, // in seconds
  materials: [materialSchema],
  order: { type: Number, required: true },
  isPublished: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Compound index for module and order
lessonSchema.index({ moduleId: 1, order: 1 }, { unique: true });

// Update updatedAt field
lessonSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

export const Lesson = mongoose.model<LessonDocument>('Lesson', lessonSchema);