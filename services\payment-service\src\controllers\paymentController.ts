import { Request, Response } from 'express';
import { Payment } from '../models/Payment';
import { PaymentStatus } from '../../shared/types';
import { xenditService } from '../services/xenditService';
import { subscriptionService } from '../services/subscriptionService';
import { logger } from '../utils/logger';
import axios from 'axios';

interface AuthRequest extends Request {
  user?: any;
}

export const createPayment = async (req: AuthRequest, res: Response) => {
  try {
    const { courseId, amount, courseDuration } = req.body;
    const studentId = req.user.id;
    const studentEmail = req.user.email;
    const studentName = `${req.user.profile.firstName} ${req.user.profile.lastName}`;

    // Check if student already has active subscription for this course
    const existingSubscription = await subscriptionService.getActiveSubscription(studentId, courseId);
    if (existingSubscription) {
      return res.status(400).json({
        success: false,
        message: 'You already have an active subscription for this course'
      });
    }

    // Get course details
    const courseServiceUrl = process.env.COURSE_SERVICE_URL || 'http://localhost:3002';
    let courseTitle = 'Course';
    try {
      const courseResponse = await axios.get(`${courseServiceUrl}/api/courses/${courseId}`);
      courseTitle = courseResponse.data.data.title;
    } catch (error) {
      logger.warn('Could not fetch course details:', error);
    }

    // Generate unique external ID
    const externalId = `TC-${Date.now()}-${studentId.slice(-6)}`;

    // Create payment record
    const payment = new Payment({
      studentId,
      courseId,
      amount,
      currency: 'IDR',
      status: PaymentStatus.PENDING,
      paymentMethod: 'xendit_invoice',
      externalId,
      expiryDate: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    });

    await payment.save();

    // Create Xendit invoice
    const invoice = await xenditService.createInvoice(
      externalId,
      amount,
      `Time Course - ${courseTitle} (${courseDuration} days)`,
      studentName,
      studentEmail
    );

    // Update payment with Xendit details
    payment.invoiceId = invoice.id;
    payment.paymentUrl = invoice.invoice_url;
    payment.expiryDate = new Date(invoice.expiry_date);
    await payment.save();

    logger.info(`Payment created: ${payment.id} for course ${courseId}`);

    res.status(201).json({
      success: true,
      data: {
        paymentId: payment.id,
        invoiceId: invoice.id,
        paymentUrl: invoice.invoice_url,
        amount: invoice.amount,
        expiryDate: invoice.expiry_date,
        externalId: externalId
      },
      message: 'Payment created successfully'
    });
  } catch (error) {
    logger.error('Error creating payment:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getPaymentStatus = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const studentId = req.user.id;

    const payment = await Payment.findOne({ _id: id, studentId });
    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // If payment is still pending, check with Xendit
    if (payment.status === PaymentStatus.PENDING && payment.invoiceId) {
      try {
        const invoice = await xenditService.getInvoice(payment.invoiceId);
        if (invoice.status === 'PAID' && payment.status !== PaymentStatus.COMPLETED) {
          // Update payment status
          payment.status = PaymentStatus.COMPLETED;
          payment.paidAt = new Date(invoice.updated);
          payment.transactionId = invoice.id;
          await payment.save();

          // Create subscription
          await subscriptionService.createSubscription(
            payment.studentId,
            payment.courseId,
            payment.id,
            90, // Default 90 days - should come from course data
            payment.amount,
            payment.currency
          );
        }
      } catch (error) {
        logger.error('Error checking payment status with Xendit:', error);
      }
    }

    res.json({
      success: true,
      data: payment
    });
  } catch (error) {
    logger.error('Error getting payment status:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getMyPayments = async (req: AuthRequest, res: Response) => {
  try {
    const studentId = req.user.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const payments = await Payment.find({ studentId })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Payment.countDocuments({ studentId });

    res.json({
      success: true,
      data: {
        payments,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error getting my payments:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};