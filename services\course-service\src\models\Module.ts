import mongoose, { Document, Schema } from 'mongoose';
import { Module as IModule } from '../../shared/types';

export interface ModuleDocument extends IModule, Document {}

const moduleSchema = new Schema<ModuleDocument>({
  courseId: { type: String, required: true, index: true },
  title: { type: String, required: true, trim: true },
  description: { type: String, required: true },
  order: { type: Number, required: true },
  isPublished: { type: Boolean, default: false },
  estimatedDuration: { type: Number }, // in minutes
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Compound index for course and order
moduleSchema.index({ courseId: 1, order: 1 }, { unique: true });

// Update updatedAt field
moduleSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

export const Module = mongoose.model<ModuleDocument>('Module', moduleSchema);