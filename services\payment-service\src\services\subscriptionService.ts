import { Subscription } from '../models/Subscription';
import { Payment } from '../models/Payment';
import { SubscriptionStatus, PaymentStatus } from '../../shared/types';
import { xenditService } from './xenditService';
import { logger } from '../utils/logger';
import axios from 'axios';

export class SubscriptionService {
  /**
   * Create subscription after successful payment
   */
  async createSubscription(
    studentId: string,
    courseId: string,
    paymentId: string,
    courseDuration: number,
    amount: number,
    currency: string = 'IDR'
  ): Promise<any> {
    try {
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(startDate.getDate() + courseDuration);

      const subscription = new Subscription({
        studentId,
        courseId,
        paymentId,
        amount,
        currency,
        startDate,
        endDate,
        status: SubscriptionStatus.ACTIVE,
        failureCount: 0
      });

      await subscription.save();

      // Initialize progress tracking in course service
      await this.initializeProgressTracking(studentId, courseId, subscription.id);

      logger.info(`Subscription created: ${subscription.id} for student ${studentId}`);
      return subscription;
    } catch (error) {
      logger.error('Error creating subscription:', error);
      throw error;
    }
  }

  /**
   * Record assignment failure and check termination
   */
  async recordAssignmentFailure(subscriptionId: string): Promise<{
    subscription: any;
    isTerminated: boolean;
    failureCount: number;
  }> {
    try {
      const subscription = await Subscription.findById(subscriptionId);
      if (!subscription) {
        throw new Error('Subscription not found');
      }

      // Increment failure count
      subscription.failureCount += 1;
      
      // Check if termination threshold is reached
      if (subscription.failureCount >= subscription.maxFailures) {
        subscription.status = SubscriptionStatus.TERMINATED;
        subscription.isEligibleForCoaching = true;
        subscription.terminatedAt = new Date();
        subscription.terminationReason = `Exceeded maximum failures (${subscription.maxFailures})`;
        
        logger.warn(`Subscription terminated: ${subscriptionId} due to ${subscription.failureCount} failures`);
      }

      await subscription.save();

      return {
        subscription,
        isTerminated: subscription.status === SubscriptionStatus.TERMINATED,
        failureCount: subscription.failureCount
      };
    } catch (error) {
      logger.error('Error recording assignment failure:', error);
      throw error;
    }
  }

  /**
   * Get active subscription for student and course
   */
  async getActiveSubscription(studentId: string, courseId: string): Promise<any> {
    try {
      const subscription = await Subscription.findOne({
        studentId,
        courseId,
        status: SubscriptionStatus.ACTIVE,
        endDate: { $gt: new Date() }
      });

      return subscription;
    } catch (error) {
      logger.error('Error getting active subscription:', error);
      throw error;
    }
  }

  /**
   * Get subscriptions eligible for coaching
   */
  async getCoachingEligibleSubscriptions(courseId?: string): Promise<any[]> {
    try {
      const filter: any = {
        status: SubscriptionStatus.TERMINATED,
        isEligibleForCoaching: true
      };

      if (courseId) {
        filter.courseId = courseId;
      }

      const subscriptions = await Subscription.find(filter)
        .sort({ terminatedAt: -1 });

      return subscriptions;
    } catch (error) {
      logger.error('Error getting coaching eligible subscriptions:', error);
      throw error;
    }
  }

  /**
   * Check and expire subscriptions
   */
  async checkExpiredSubscriptions(): Promise<void> {
    try {
      const expiredSubscriptions = await Subscription.find({
        status: SubscriptionStatus.ACTIVE,
        endDate: { $lt: new Date() }
      });

      for (const subscription of expiredSubscriptions) {
        subscription.status = SubscriptionStatus.EXPIRED;
        await subscription.save();
        
        logger.info(`Subscription expired: ${subscription.id}`);
      }
    } catch (error) {
      logger.error('Error checking expired subscriptions:', error);
    }
  }

  /**
   * Initialize progress tracking in course service
   */
  private async initializeProgressTracking(studentId: string, courseId: string, subscriptionId: string): Promise<void> {
    try {
      const courseServiceUrl = process.env.COURSE_SERVICE_URL || 'http://localhost:3002';
      
      await axios.post(`${courseServiceUrl}/api/progress/initialize`, {
        studentId,
        courseId,
        subscriptionId
      });

      logger.info(`Progress tracking initialized for subscription: ${subscriptionId}`);
    } catch (error) {
      logger.error('Error initializing progress tracking:', error);
      // Don't throw error as this is not critical for subscription creation
    }
  }
}

export const subscriptionService = new SubscriptionService();