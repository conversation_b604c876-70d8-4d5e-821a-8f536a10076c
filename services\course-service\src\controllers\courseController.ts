import { Request, Response } from 'express';
import { Course } from '../models/Course';
import { Module } from '../models/Module';
import { Lesson } from '../models/Lesson';
import { CourseStatus, UserRole } from '../../shared/types';
import { logger } from '../utils/logger';

interface AuthRequest extends Request {
  user?: any;
}

export const createCourse = async (req: AuthRequest, res: Response) => {
  try {
    const { title, description, category, duration, price, currency, tags, requirements, learningOutcomes } = req.body;
    const tutorId = req.user.id;

    // Only tutors and admins can create courses
    if (req.user.role !== UserRole.TUTOR && req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Only tutors and admins can create courses'
      });
    }

    const course = new Course({
      title,
      description,
      category,
      tutorId,
      duration,
      totalModules: 0, // Will be updated when modules are added
      price,
      currency: currency || 'USD',
      tags: tags || [],
      requirements: requirements || [],
      learningOutcomes: learningOutcomes || [],
      status: CourseStatus.DRAFT
    });

    await course.save();

    logger.info(`Course created: ${course.title} by tutor ${tutorId}`);

    res.status(201).json({
      success: true,
      data: course,
      message: 'Course created successfully'
    });
  } catch (error) {
    logger.error('Error creating course:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getAllCourses = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const category = req.query.category as string;
    const status = req.query.status as CourseStatus;
    const search = req.query.search as string;

    const filter: any = {};
    
    if (category) {
      filter.category = category;
    }
    
    if (status) {
      filter.status = status;
    } else {
      // Default to published courses for public access
      filter.status = CourseStatus.PUBLISHED;
    }

    if (search) {
      filter.$text = { $search: search };
    }

    const courses = await Course.find(filter)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Course.countDocuments(filter);

    res.json({
      success: true,
      data: {
        courses,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error getting courses:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getCourseById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const course = await Course.findById(id);

    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    // Get course modules and lessons
    const modules = await Module.find({ courseId: id }).sort({ order: 1 });
    const moduleIds = modules.map(m => m.id);
    const lessons = await Lesson.find({ moduleId: { $in: moduleIds } }).sort({ order: 1 });

    const courseWithContent = {
      ...course.toObject(),
      modules: modules.map(module => ({
        ...module.toObject(),
        lessons: lessons.filter(lesson => lesson.moduleId === module.id)
      }))
    };

    res.json({
      success: true,
      data: courseWithContent
    });
  } catch (error) {
    logger.error('Error getting course by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const updateCourse = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    const course = await Course.findById(id);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    // Check if user can update this course
    if (req.user.role !== UserRole.ADMIN && course.tutorId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only update your own courses'
      });
    }

    const updatedCourse = await Course.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    );

    res.json({
      success: true,
      data: updatedCourse,
      message: 'Course updated successfully'
    });
  } catch (error) {
    logger.error('Error updating course:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const deleteCourse = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    const course = await Course.findById(id);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    // Check if user can delete this course
    if (req.user.role !== UserRole.ADMIN && course.tutorId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only delete your own courses'
      });
    }

    // Delete associated modules and lessons
    const modules = await Module.find({ courseId: id });
    const moduleIds = modules.map(m => m.id);
    
    await Lesson.deleteMany({ moduleId: { $in: moduleIds } });
    await Module.deleteMany({ courseId: id });
    await Course.findByIdAndDelete(id);

    res.json({
      success: true,
      message: 'Course deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting course:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const publishCourse = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    const course = await Course.findById(id);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Course not found'
      });
    }

    // Check if user can publish this course
    if (req.user.role !== UserRole.ADMIN && course.tutorId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only publish your own courses'
      });
    }

    // Validate course has content before publishing
    const moduleCount = await Module.countDocuments({ courseId: id });
    if (moduleCount === 0) {
      return res.status(400).json({
        success: false,
        message: 'Course must have at least one module before publishing'
      });
    }

    course.status = CourseStatus.PUBLISHED;
    course.totalModules = moduleCount;
    await course.save();

    res.json({
      success: true,
      data: course,
      message: 'Course published successfully'
    });
  } catch (error) {
    logger.error('Error publishing course:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getMyCourses = async (req: AuthRequest, res: Response) => {
  try {
    const tutorId = req.user.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const courses = await Course.find({ tutorId })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Course.countDocuments({ tutorId });

    res.json({
      success: true,
      data: {
        courses,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error getting my courses:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};