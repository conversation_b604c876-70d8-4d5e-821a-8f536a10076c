import express from 'express';
import { body } from 'express-validator';
import { validate } from '../middleware/validation';
import { auth, authorize } from '../middleware/auth';
import { UserRole } from '../../shared/types';
import {
  createPayment,
  getPaymentStatus,
  getMyPayments
} from '../controllers/paymentController';

const router = express.Router();

// Create payment (student only)
router.post('/', [
  auth,
  authorize(UserRole.STUDENT),
  body('courseId').notEmpty().withMessage('Course ID is required'),
  body('amount').isFloat({ min: 0 }).withMessage('Amount must be a positive number'),
  body('courseDuration').isInt({ min: 1 }).withMessage('Course duration must be a positive integer'),
  validate
], createPayment);

// Get payment status
router.get('/:id', auth, getPaymentStatus);

// Get my payments (student only)
router.get('/my/payments', auth, authorize(UserRole.STUDENT), getMyPayments);

export default router;