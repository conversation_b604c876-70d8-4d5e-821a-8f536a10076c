import express from 'express';
import { auth, authorize } from '../middleware/auth';
import { UserRole } from '../../shared/types';
import { dataCollectionService } from '../services/dataCollectionService';
import { logger } from '../utils/logger';

const router = express.Router();

interface AuthRequest extends express.Request {
  user?: any;
}

// Trigger manual data collection (Admin only)
router.post('/collect', auth, authorize(UserRole.ADMIN), async (req: AuthRequest, res) => {
  try {
    await dataCollectionService.collectAllData();
    
    res.json({
      success: true,
      message: 'Data collection completed successfully'
    });
  } catch (error) {
    logger.error('Error in manual data collection:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get platform analytics (Admin only)
router.get('/platform', auth, authorize(UserRole.ADMIN), async (req: AuthRequest, res) => {
  try {
    const dashboard = await dataCollectionService.getPlatformDashboard();
    
    res.json({
      success: true,
      data: dashboard
    });
  } catch (error) {
    logger.error('Error getting platform analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get tutor analytics
router.get('/tutor/:tutorId', auth, authorize(UserRole.TUTOR, UserRole.ADMIN), async (req: AuthRequest, res) => {
  try {
    const { tutorId } = req.params;
    
    // Tutors can only view their own analytics unless they're admin
    if (req.user.role !== UserRole.ADMIN && req.user.id !== tutorId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    const dashboard = await dataCollectionService.getTutorDashboard(tutorId);
    
    res.json({
      success: true,
      data: dashboard
    });
  } catch (error) {
    logger.error('Error getting tutor analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

export default router;