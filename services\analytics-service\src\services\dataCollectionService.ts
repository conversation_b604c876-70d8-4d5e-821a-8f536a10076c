import axios from 'axios';
import cron from 'node-cron';
import { PlatformAnalytics, CourseAnalytics, UserAnalytics } from '../models/Analytics';
import { logger } from '../utils/logger';

const AUTH_SERVICE_URL = process.env.AUTH_SERVICE_URL || 'http://localhost:3001';
const COURSE_SERVICE_URL = process.env.COURSE_SERVICE_URL || 'http://localhost:3002';
const PAYMENT_SERVICE_URL = process.env.PAYMENT_SERVICE_URL || 'http://localhost:3003';

export class DataCollectionService {
  /**
   * Collect data from all services and update analytics
   */
  async collectAllData(): Promise<void> {
    try {
      logger.info('Starting data collection...');
      
      await Promise.all([
        this.collectPlatformAnalytics(),
        this.collectCourseAnalytics(),
        this.collectUserAnalytics()
      ]);
      
      logger.info('Data collection completed successfully');
    } catch (error) {
      logger.error('Error during data collection:', error);
    }
  }

  /**
   * Collect platform-wide analytics
   */
  private async collectPlatformAnalytics(): Promise<void> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get data from all services
      const [usersData, subscriptionsData, paymentsData] = await Promise.all([
        this.fetchFromService(`${AUTH_SERVICE_URL}/api/users?limit=1000`),
        this.fetchFromService(`${PAYMENT_SERVICE_URL}/api/subscriptions?limit=1000`),
        this.fetchFromService(`${PAYMENT_SERVICE_URL}/api/payments/my/payments?limit=1000`)
      ]);

      const totalUsers = usersData?.data?.total || 0;
      const activeSubscriptions = subscriptionsData?.data?.subscriptions?.filter((s: any) => s.status === 'active').length || 0;
      const totalRevenue = paymentsData?.data?.payments?.reduce((sum: number, p: any) => 
        p.status === 'completed' ? sum + p.amount : sum, 0) || 0;

      // Update or create platform analytics
      await PlatformAnalytics.findOneAndUpdate(
        { date: today },
        {
          totalUsers,
          activeSubscriptions,
          totalRevenue,
          lastUpdated: new Date()
        },
        { upsert: true, new: true }
      );

      logger.info(`Platform analytics updated: ${totalUsers} users, ${activeSubscriptions} active subscriptions`);
    } catch (error) {
      logger.error('Error collecting platform analytics:', error);
    }
  }

  /**
   * Collect course-specific analytics
   */
  private async collectCourseAnalytics(): Promise<void> {
    try {
      const coursesData = await this.fetchFromService(`${COURSE_SERVICE_URL}/api/courses?limit=1000`);
      const courses = coursesData?.data?.courses || [];

      for (const course of courses) {
        // Get subscriptions for this course
        const subscriptionsData = await this.fetchFromService(
          `${PAYMENT_SERVICE_URL}/api/subscriptions?courseId=${course.id}`
        );
        const subscriptions = subscriptionsData?.data?.subscriptions || [];

        const totalEnrollments = subscriptions.length;
        const activeEnrollments = subscriptions.filter((s: any) => s.status === 'active').length;
        const completionRate = totalEnrollments > 0 ? 
          (subscriptions.filter((s: any) => s.progress?.completionPercentage === 100).length / totalEnrollments) * 100 : 0;

        await CourseAnalytics.findOneAndUpdate(
          { courseId: course.id },
          {
            tutorId: course.tutorId,
            title: course.title,
            category: course.category,
            totalEnrollments,
            activeEnrollments,
            completionRate,
            lastUpdated: new Date()
          },
          { upsert: true, new: true }
        );
      }

      logger.info(`Course analytics updated for ${courses.length} courses`);
    } catch (error) {
      logger.error('Error collecting course analytics:', error);
    }
  }

  /**
   * Collect user-specific analytics
   */
  private async collectUserAnalytics(): Promise<void> {
    try {
      const usersData = await this.fetchFromService(`${AUTH_SERVICE_URL}/api/users?limit=1000`);
      const users = usersData?.data?.users || [];

      for (const user of users) {
        if (user.role === 'student') {
          // Get user's subscriptions
          const subscriptionsData = await this.fetchFromService(
            `${PAYMENT_SERVICE_URL}/api/subscriptions?studentId=${user.id}`
          );
          const subscriptions = subscriptionsData?.data?.subscriptions || [];

          const totalCourses = subscriptions.length;
          const activeSubscriptions = subscriptions.filter((s: any) => s.status === 'active').length;
          const completedCourses = subscriptions.filter((s: any) => 
            s.progress?.completionPercentage === 100).length;

          await UserAnalytics.findOneAndUpdate(
            { userId: user.id },
            {
              role: user.role,
              totalCourses,
              completedCourses,
              activeSubscriptions,
              lastActivity: user.lastLogin || user.updatedAt,
              joinedDate: user.createdAt
            },
            { upsert: true, new: true }
          );
        }
      }

      logger.info(`User analytics updated for ${users.length} users`);
    } catch (error) {
      logger.error('Error collecting user analytics:', error);
    }
  }

  /**
   * Fetch data from a service with error handling
   */
  private async fetchFromService(url: string): Promise<any> {
    try {
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error: any) {
      logger.warn(`Failed to fetch from ${url}:`, error.message);
      return null;
    }
  }

  /**
   * Get platform dashboard data
   */
  async getPlatformDashboard(): Promise<any> {
    try {
      const today = new Date();
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      const [latestAnalytics, monthlyTrend, topCourses] = await Promise.all([
        PlatformAnalytics.findOne().sort({ date: -1 }),
        PlatformAnalytics.find({ 
          date: { $gte: thirtyDaysAgo } 
        }).sort({ date: 1 }),
        CourseAnalytics.find().sort({ totalEnrollments: -1 }).limit(10)
      ]);

      return {
        overview: latestAnalytics || {},
        monthlyTrend: monthlyTrend || [],
        topCourses: topCourses || []
      };
    } catch (error) {
      logger.error('Error getting platform dashboard:', error);
      throw error;
    }
  }

  /**
   * Get tutor dashboard data
   */
  async getTutorDashboard(tutorId: string): Promise<any> {
    try {
      const [tutorCourses, totalStudents] = await Promise.all([
        CourseAnalytics.find({ tutorId }).sort({ totalEnrollments: -1 }),
        CourseAnalytics.aggregate([
          { $match: { tutorId } },
          { $group: { _id: null, total: { $sum: '$totalEnrollments' } } }
        ])
      ]);

      return {
        courses: tutorCourses || [],
        totalStudents: totalStudents[0]?.total || 0,
        totalCourses: tutorCourses.length
      };
    } catch (error) {
      logger.error('Error getting tutor dashboard:', error);
      throw error;
    }
  }
}

export const dataCollectionService = new DataCollectionService();

/**
 * Start automated data collection
 */
export function startDataCollection(): void {
  // Run data collection every hour
  cron.schedule('0 * * * *', async () => {
    await dataCollectionService.collectAllData();
  });

  // Run initial collection after 30 seconds
  setTimeout(async () => {
    await dataCollectionService.collectAllData();
  }, 30000);

  logger.info('Data collection service started');
}