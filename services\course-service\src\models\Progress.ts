import mongoose, { Document, Schema } from 'mongoose';
import { Progress as IProgress } from '../../shared/types';

export interface ProgressDocument extends IProgress, Document {}

const progressSchema = new Schema<ProgressDocument>({
  studentId: { type: String, required: true, index: true },
  courseId: { type: String, required: true, index: true },
  subscriptionId: { type: String, required: true },
  completedLessons: [{ type: String }],
  completedAssignments: [{ type: String }],
  totalLessons: { type: Number, required: true },
  totalAssignments: { type: Number, required: true },
  completionPercentage: { type: Number, default: 0, min: 0, max: 100 },
  currentModule: { type: Number, default: 1 },
  lastAccessedAt: { type: Date, default: Date.now },
  startedAt: { type: Date, default: Date.now },
  completedAt: { type: Date },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Compound index for student and course
progressSchema.index({ studentId: 1, courseId: 1 }, { unique: true });
progressSchema.index({ subscriptionId: 1 });

// Update updatedAt field
progressSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  this.lastAccessedAt = new Date();
  next();
});

export const Progress = mongoose.model<ProgressDocument>('Progress', progressSchema);