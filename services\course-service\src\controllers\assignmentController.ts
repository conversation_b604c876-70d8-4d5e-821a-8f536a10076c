import { Request, Response } from 'express';
import { Assignment } from '../models/Assignment';
import { AssignmentSubmission } from '../models/AssignmentSubmission';
import { Lesson } from '../models/Lesson';
import { progressService } from '../services/progressService';
import { UserRole } from '../../shared/types';
import { logger } from '../utils/logger';
import axios from 'axios';

interface AuthRequest extends Request {
  user?: any;
}

export const createAssignment = async (req: AuthRequest, res: Response) => {
  try {
    const { lessonId, title, description, questions, triggerTime, timeLimit, passingScore, maxAttempts } = req.body;

    // Verify lesson exists and user has permission
    const lesson = await Lesson.findById(lessonId);
    if (!lesson) {
      return res.status(404).json({
        success: false,
        message: 'Lesson not found'
      });
    }

    // Only tutors and admins can create assignments
    if (req.user.role !== UserRole.TUTOR && req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Only tutors and admins can create assignments'
      });
    }

    const assignment = new Assignment({
      lessonId,
      title,
      description,
      questions: questions.map((q: any, index: number) => ({
        ...q,
        order: index + 1
      })),
      triggerTime,
      timeLimit,
      passingScore: passingScore || 100, // Default to 100% for strict evaluation
      maxAttempts: maxAttempts || 1
    });

    await assignment.save();

    logger.info(`Assignment created: ${assignment.title} for lesson ${lessonId}`);

    res.status(201).json({
      success: true,
      data: assignment,
      message: 'Assignment created successfully'
    });
  } catch (error) {
    logger.error('Error creating assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getAssignmentsByLesson = async (req: Request, res: Response) => {
  try {
    const { lessonId } = req.params;

    const assignments = await Assignment.find({ lessonId, isActive: true })
      .sort({ triggerTime: 1 });

    res.json({
      success: true,
      data: assignments
    });
  } catch (error) {
    logger.error('Error getting assignments by lesson:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getAssignmentById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const assignment = await Assignment.findById(id);
    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Assignment not found'
      });
    }

    res.json({
      success: true,
      data: assignment
    });
  } catch (error) {
    logger.error('Error getting assignment by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const submitAssignment = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { answers, subscriptionId, timeSpent } = req.body;
    const studentId = req.user.id;

    // Get assignment
    const assignment = await Assignment.findById(id);
    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Assignment not found'
      });
    }

    // Check if student has already reached max attempts
    const previousAttempts = await AssignmentSubmission.countDocuments({
      studentId,
      assignmentId: id
    });

    if (previousAttempts >= assignment.maxAttempts) {
      return res.status(400).json({
        success: false,
        message: 'Maximum attempts reached for this assignment'
      });
    }

    // Grade the assignment
    const gradedAnswers = assignment.questions.map(question => {
      const studentAnswer = answers.find((a: any) => a.questionId === question.id);
      const isCorrect = studentAnswer && 
        studentAnswer.answer.toLowerCase().trim() === question.correctAnswer.toLowerCase().trim();
      
      return {
        questionId: question.id,
        answer: studentAnswer?.answer || '',
        isCorrect,
        points: isCorrect ? question.points : 0
      };
    });

    // Calculate score
    const totalPoints = assignment.questions.reduce((sum, q) => sum + q.points, 0);
    const earnedPoints = gradedAnswers.reduce((sum, a) => sum + a.points, 0);
    const score = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0;
    const isPassed = score >= assignment.passingScore;

    // Create submission record
    const submission = new AssignmentSubmission({
      studentId,
      assignmentId: id,
      subscriptionId,
      answers: gradedAnswers,
      score,
      isPassed,
      attemptNumber: previousAttempts + 1,
      timeSpent
    });

    await submission.save();

    // Update progress if passed
    if (isPassed) {
      const lesson = await Lesson.findById(assignment.lessonId);
      if (lesson) {
        // Get course ID from lesson's module
        const { Module } = require('../models/Module');
        const module = await Module.findById(lesson.moduleId);
        if (module) {
          await progressService.completeAssignment(studentId, module.courseId, id);
        }
      }
    } else {
      // Record failure in payment service
      try {
        const paymentServiceUrl = process.env.PAYMENT_SERVICE_URL || 'http://localhost:3003';
        await axios.post(`${paymentServiceUrl}/api/subscriptions/record-failure`, {
          subscriptionId
        });
        logger.info(`Assignment failure recorded for subscription: ${subscriptionId}`);
      } catch (error) {
        logger.error('Error recording assignment failure:', error);
      }
    }

    logger.info(`Assignment submitted: ${id} by student ${studentId}, score: ${score}%`);

    res.json({
      success: true,
      data: {
        submission,
        isPassed,
        score,
        feedback: isPassed ? 'Congratulations! You passed the assignment.' : 
                 `You scored ${score}%. You need ${assignment.passingScore}% to pass.`
      },
      message: isPassed ? 'Assignment passed successfully' : 'Assignment failed'
    });
  } catch (error) {
    logger.error('Error submitting assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getAssignmentSubmissions = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const studentId = req.user.id;

    const submissions = await AssignmentSubmission.find({
      assignmentId: id,
      studentId
    }).sort({ submittedAt: -1 });

    res.json({
      success: true,
      data: submissions
    });
  } catch (error) {
    logger.error('Error getting assignment submissions:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const updateAssignment = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Only tutors and admins can update assignments
    if (req.user.role !== UserRole.TUTOR && req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Only tutors and admins can update assignments'
      });
    }

    const assignment = await Assignment.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    );

    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Assignment not found'
      });
    }

    res.json({
      success: true,
      data: assignment,
      message: 'Assignment updated successfully'
    });
  } catch (error) {
    logger.error('Error updating assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const deleteAssignment = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    // Only tutors and admins can delete assignments
    if (req.user.role !== UserRole.TUTOR && req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Only tutors and admins can delete assignments'
      });
    }

    const assignment = await Assignment.findByIdAndDelete(id);
    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Assignment not found'
      });
    }

    // Also delete all submissions for this assignment
    await AssignmentSubmission.deleteMany({ assignmentId: id });

    res.json({
      success: true,
      message: 'Assignment deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};